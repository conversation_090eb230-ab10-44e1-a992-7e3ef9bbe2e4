<cfcomponent>
	<!--- testmode settings:
		0: no testing. real deal.   
		1: testing using auth test request.    
	--->
	<cfif application.settings.environment neq "production">
		<cfset variables.x_testmode = 1>
		<cfset variables.x_solution_id = "AAA100302"> <!--- this is auth sandbox's solution id --->
	<cfelse>
		<cfset variables.x_testmode = 0>
		<cfset variables.x_solution_id = "AAA174267"> <!--- this is membercentral's solution id --->
	</cfif>

	<cfset variables.requesttimeout="60">	

	<cffunction name="gather" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="websiteOrgcode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.jsvalidation = ''>
		<cfset local.returnStruct.inputForm = ''>
		<cfset local.returnStruct.headcode = ''>

		<cfquery name="local.qryProfilesOnFile" datasource="#application.settings.dsn.trialsmith.dsn#">
			select payProfileID, detail, depomemberdataid, customerProfileID, paymentProfileID, expiration
			from dbo.ccMemberPaymentProfiles
			where orgcode = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and customerid = <cfqueryparam value="#arguments.customerid#" cfsqltype="CF_SQL_VARCHAR">
		</cfquery>

		<!--- customer profileID to use for adding new profiles --->
		<cfquery name="local.qryCustProfile" dbtype="query" maxrows="1">
			select customerProfileID, count(*) as theCount
			from [local].qryProfilesOnFile
			group by customerProfileID
			order by theCount
		</cfquery>		

		<cfset local.cidToUse = arguments.customerid>
		<cfset local.cpidToUse = local.qryCustProfile.customerProfileID>

		<cfset local.EncAddPayProfile = "<data><action>addPaymentProfile</action><mo>#xmlformat(arguments.merchantOrgcode)#</mo><cid>#arguments.customerID#</cid><wo>#xmlformat(arguments.websiteOrgcode)#</wo></data>">
		<cfset local.EncAddPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
		<cfset local.EncAddPayProfileReturn = "<data><action>addPaymentProfileReturn</action><mo>#xmlformat(arguments.merchantOrgcode)#</mo><cpid>#local.qryCustProfile.customerProfileID#</cpid><cid>#arguments.customerid#</cid><wo>#xmlformat(arguments.websiteOrgcode)#</wo></data>">
		<cfset local.EncAddPayProfileReturn = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncAddPayProfileReturn,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>

		<cfsavecontent variable="local.returnStruct.headcode">
			<cfoutput>
			<style type="text/css">
				##p_#arguments.merchantOrgcode#_DIV a.cof_edit { background:url(/media/grid/creditcards.png) no-repeat left center; padding-left:20px; }
				##p_#arguments.merchantOrgcode#_DIV a.cof_add { background:url(/media/grid/add.png) no-repeat left center; padding-left:20px; }
				##p_#arguments.merchantOrgcode#_DIV td { line-height:17px; margin-bottom:4px; }
			</style>
			<script language="javascript">
				$(function() {
					if ($('##divBuyNowPopup').length == 0)
						$('##CIMTable').after('<div style="text-align:center;display:none;" id="divBuyNowPopupLoading"><br/><img src="/media/indicator.gif" width="100" height="100"><br/><br/><div class="tsAppHeading">Please wait while we load the secure payment form.</div></div><div id="divBuyNowPopup" style="display:none;"><iframe name="iframeBuyNow" id="iframeBuyNow" frameborder="0" scrolling="no"></iframe></div>');
				});

				function p_#arguments.merchantOrgcode#_addPaymentProfile() {
					hideAlert();
					$('##CIMTable').hide();
					$('##caserefTable').hide();
					$('input.appContinueBtn').attr("disabled","disabled");
					$('##divBuyNowPopup').hide();
					$('##divBuyNowPopupLoading').show();
					$('##iframeBuyNow').attr('width','435px').attr('height','438px').attr('src','/scheduled/buyNowCIM.cfm?nobanner=1&wizard=#local.EncAddPayProfile#');
					$('##divBuyNowPopupLoading').hide();
					$('##divBuyNowPopup').show();
				}
				function p_#arguments.merchantOrgcode#_addPaymentProfileReturn(obj) {
					$('##divBuyNowPopup').hide();
					$('##CIMTable').show();
					if (obj && obj.a && obj.a=='save') {
						$('##p_#arguments.merchantOrgcode#_DIV').attr('pofcount','0').html('Reloading... please wait.');
						$('##iframeBuyNow').attr('src','/scheduled/buyNowCIM.cfm?nobanner=1&wizard=#JSStringFormat(local.EncAddPayProfileReturn)#');
					} else {
						$('##iframeBuyNow').attr('width','1px').attr('height','1px').attr('src','about:blank');
						$('input.appContinueBtn').removeAttr("disabled");
						$('##caserefTable').show();
					}
					if (obj && obj.err) showAlert(obj.err);
				}
				
				function p_#arguments.merchantOrgcode#_editPaymentProfile(we) {
					hideAlert();
					$('##CIMTable').hide();
					$('##caserefTable').hide();
					$('input.appContinueBtn').attr("disabled","disabled");
					$('##divBuyNowPopup').hide();
					$('##divBuyNowPopupLoading').show();
					$('##iframeBuyNow').attr('width','435px').attr('height','438px').attr('src','/scheduled/buyNowCIM.cfm?nobanner=1&wizard=' + escape(we));
					$('##divBuyNowPopupLoading').hide();
					$('##divBuyNowPopup').show();
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.returnStruct.jsvalidation">
			<cfoutput>
			if ($('##p_#arguments.merchantOrgcode#_DIV').attr("pofcount") == 0) arrReq[arrReq.length] = 'Enter your credit card information to continue.';
			</cfoutput>
		</cfsavecontent>

		<cfsavecontent variable="local.returnStruct.inputForm">
			<cfoutput>
			<div id="p_#arguments.merchantOrgcode#_DIV" pofcount="#local.qryProfilesOnFile.recordcount#">
				<cfif local.qryProfilesOnFile.recordcount gt 0>
					<cfif local.qryProfilesOnFile.recordcount is 1>
						<div>Use the following credit card:</div>
					<cfelse>
						<div>Select from the following credit cards:</div>
					</cfif>
					<table class="tsAppBodyText" style="margin-top:4px;">
					<cfloop query="local.qryProfilesOnFile">
						<cfset local.EncEditPayProfile = "<data><action>editPaymentProfile</action><mo>#xmlformat(arguments.merchantOrgcode)#</mo><cpid>#local.qryProfilesOnFile.customerProfileID#</cpid><cppid>#local.qryProfilesOnFile.paymentProfileID#</cppid><wo>#xmlformat(arguments.websiteOrgcode)#</wo></data>">
						<cfset local.EncEditPayProfile = Replace(URLEncodedFormat(ToBase64(Encrypt(local.EncEditPayProfile,'M3mberC3ntr@l^_2012@'))),"%","xPcmKx","ALL")>
						<tr valign="top">
							<td><input type="radio" name="p_#arguments.merchantOrgcode#_mppid" value="#payProfileID#" <cfif currentrow is 1>checked</cfif>></td>
							<td><b>#detail#</b><cfif len(local.qryProfilesOnFile.expiration)>&nbsp;Exp #DateFormat(local.qryProfilesOnFile.expiration,"mm/yy")#</cfif></td>
							<td width="20">&nbsp;</td>
							<td><a href="javascript:p_#arguments.merchantOrgcode#_editPaymentProfile('#JSStringFormat(local.EncEditPayProfile)#')" class="cof_edit">Edit Card</a></td>
						</tr>
					</cfloop>
					</table>
					<br/>
				</cfif>
				<cfif local.qryProfilesOnFile.recordcount is 0>
					<div>
						<a href="javascript:p_#arguments.merchantOrgcode#_addPaymentProfile()" class="cof_add">Enter your credit card information</a> to continue.
					</div>
				</cfif>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="createCustomerProfile" access="private" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.customerProfileId=''>
		<cfset local.returnStruct.head=''>

		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>

		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<createCustomerProfileRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#local.qryGetCIMInfo.authUsername#</name>
						<transactionKey>#local.qryGetCIMInfo.authTransKey#</transactionKey>
					</merchantAuthentication>
					<profile>
						<merchantCustomerId>#XMLFormat(left(arguments.customerID,20))#</merchantCustomerId>
					</profile>
					<validationMode>none</validationMode>
				</createCustomerProfileRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfif local.apiResponse.responseCode is 1>
				<cfset local.returnStruct.customerProfileId = XMLSearch(local.apiResponse.rawresponse,"/*/customerProfileId")>
				<cfif arrayLen(local.returnStruct.customerProfileId) is 1><cfset local.returnStruct.customerProfileId = local.returnStruct.customerProfileId[1].xmlText><cfelse><cfset local.returnStruct.customerProfileId = ""></cfif>
			<cfelse>
				<!--- need to catch if profile could not be created because it already exists --->
				<cfif findNoCase("[E00039]",local.apiResponse.responseReasonText)>

					<!--- java needed because cf doesnt support look ahead/behinds --->
					<cfset local.objPattern = CreateObject("java","java.util.regex.Pattern").Compile("(?i)(?<=\[E00039\] A duplicate record with ID )([0-9]+)(?= already exists\.)")>
					<cfset local.objMatcher = local.objPattern.Matcher(local.apiResponse.responseReasonText)>
					<cfset local.arrIDs = arrayNew(1)>
					<cfloop condition="local.objMatcher.Find()">
						<cfset ArrayAppend(local.arrIDs,local.objMatcher.Group())>
					</cfloop>
					<cfif arrayLen(local.arrIDs) is 1>
						<cfset local.returnStruct.customerProfileId = local.arrIDs[1]>
					</cfif>
				<cfelse>
					<cfthrow message="Error creating customer profile. #local.apiResponse.responseReasonText#">
				</cfif>
			</cfif>
			
			<!--- error if we dont have a customerProfileID now. --->
			<cfif not len(local.returnStruct.customerProfileid)>
				<cfthrow message="Error creating customer profile.">
			</cfif>
			
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>
		
		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="addPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantorgcode" type="string" required="yes">
		<cfargument name="websiteOrgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.html=''>
		<cfset local.returnStruct.head=''>

		<!--- create customer profile id --->
		<cfset local.strCustProfile = createCustomerProfile(merchantorgcode=arguments.merchantorgcode, customerID=arguments.customerID)>
		<cfset local.customerProfileid = local.strCustProfile.customerProfileID>
		<cfif len(local.strCustProfile.head)>
			<cfset local.returnStruct.head = local.strCustProfile.head>
			<cfreturn local.returnStruct>
		</cfif>

		<!--- prefill data --->
		<cfset local.strPrefill = structNew()> 
		<cfif left(arguments.customerID,7) eq "olddid_">
			<cfquery name="local.qryGetData" datasource="#application.settings.dsn.trialsmith.dsn#">
				select firstname, lastname, billingAddress, billingCity, billingState, billingZIP
				from dbo.depomemberdata 
				where depomemberdataid = <cfqueryparam cfsqltype="cf_sql_integer" value="#GetToken(arguments.customerID,2,"_")#">
			</cfquery>				
			<cfset local.strPrefill.fld_1_ = local.qryGetData.firstname>
			<cfset local.strPrefill.fld_2_ = local.qryGetData.lastname>
			<cfset local.strPrefill.fld_4_ = ''>
			<cfset local.strPrefill.fld_6_ = ''>
			<cfset local.strPrefill.fld_11_ = local.qryGetData.billingZIP>
			<cfset local.strPrefill.fld_12_ = local.qryGetData.billingAddress>
			<cfset local.strPrefill.fld_13_ = local.qryGetData.billingCity>
			<cfset local.strPrefill.fld_14_ = local.qryGetData.billingState>
			<cfset local.strPrefill.fld_15_ = ''>
		<cfelse>
			<cfset local.strPrefill.fld_1_ = ''>
			<cfset local.strPrefill.fld_2_ = ''>
			<cfset local.strPrefill.fld_4_ = ''>
			<cfset local.strPrefill.fld_6_ = ''>
			<cfset local.strPrefill.fld_11_ = ''>
			<cfset local.strPrefill.fld_12_ = ''>
			<cfset local.strPrefill.fld_13_ = ''>
			<cfset local.strPrefill.fld_14_ = ''>
			<cfset local.strPrefill.fld_15_ = ''>
		</cfif>
		
		
		<cfreturn showPaymentProfileForm(merchantorgcode=arguments.merchantorgcode, websiteOrgcode=arguments.websiteOrgcode, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill)>
	</cffunction>

	<cffunction name="editPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="websiteOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.html=''>
		<cfset local.returnStruct.head=''>

		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>

		<!--- get payment profile info from CIM --->
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<getCustomerPaymentProfileRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#local.qryGetCIMInfo.authUsername#</name>
						<transactionKey>#local.qryGetCIMInfo.authTransKey#</transactionKey>
					</merchantAuthentication>
					<customerProfileId>#XMLFormat(arguments.customerProfileid)#</customerProfileId>
					<customerPaymentProfileId>#XMLFormat(arguments.customerPaymentProfileId)#</customerPaymentProfileId>
				</getCustomerPaymentProfileRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfif local.apiResponse.responseCode is 1>
				<cfset local.strPrefill = structNew()> 
				<cfset local.strPrefill.fld_1_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/billTo/firstName')>
					<cfif arrayLen(local.strPrefill.fld_1_) is 1><cfset local.strPrefill.fld_1_ = local.strPrefill.fld_1_[1].xmlText><cfelse><cfset local.strPrefill.fld_1_ = ""></cfif>
				<cfset local.strPrefill.fld_2_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/billTo/lastName')>
					<cfif arrayLen(local.strPrefill.fld_2_) is 1><cfset local.strPrefill.fld_2_ = local.strPrefill.fld_2_[1].xmlText><cfelse><cfset local.strPrefill.fld_2_ = ""></cfif>
				<cfset local.strPrefill.fld_4_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/payment/creditCard/cardNumber')>
					<cfif arrayLen(local.strPrefill.fld_4_) is 1><cfset local.strPrefill.fld_4_ = local.strPrefill.fld_4_[1].xmlText><cfelse><cfset local.strPrefill.fld_4_ = ""></cfif>
				<cfset local.strPrefill.fld_6_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/payment/creditCard/expirationDate')>
					<cfif arrayLen(local.strPrefill.fld_6_) is 1><cfset local.strPrefill.fld_6_ = local.strPrefill.fld_6_[1].xmlText><cfelse><cfset local.strPrefill.fld_6_ = ""></cfif>
				<cfset local.strPrefill.fld_11_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/billTo/zip')>
					<cfif arrayLen(local.strPrefill.fld_11_) is 1><cfset local.strPrefill.fld_11_ = local.strPrefill.fld_11_[1].xmlText><cfelse><cfset local.strPrefill.fld_11_ = ""></cfif>
				<cfset local.strPrefill.fld_12_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/billTo/address')>
					<cfif arrayLen(local.strPrefill.fld_12_) is 1><cfset local.strPrefill.fld_12_ = local.strPrefill.fld_12_[1].xmlText><cfelse><cfset local.strPrefill.fld_12_ = ""></cfif>
				<cfset local.strPrefill.fld_13_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/billTo/city')>
					<cfif arrayLen(local.strPrefill.fld_13_) is 1><cfset local.strPrefill.fld_13_ = local.strPrefill.fld_13_[1].xmlText><cfelse><cfset local.strPrefill.fld_13_ = ""></cfif>
				<cfset local.strPrefill.fld_14_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/billTo/state')>
					<cfif arrayLen(local.strPrefill.fld_14_) is 1><cfset local.strPrefill.fld_14_ = local.strPrefill.fld_14_[1].xmlText><cfelse><cfset local.strPrefill.fld_14_ = ""></cfif>
				<cfset local.strPrefill.fld_15_ = XMLSearch(local.apiResponse.rawresponse,'/*/paymentProfile/billTo/country')>
					<cfif arrayLen(local.strPrefill.fld_15_) is 1><cfset local.strPrefill.fld_15_ = local.strPrefill.fld_15_[1].xmlText><cfelse><cfset local.strPrefill.fld_15_ = ""></cfif>
			<cfelse>
				<cfthrow message="Unable to locate credit card for editing. #local.apiResponse.responseReasonText#">
			</cfif>
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantorgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantorgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>	

		<cfreturn showPaymentProfileForm(merchantorgcode=arguments.merchantorgcode, websiteOrgcode=arguments.websiteOrgcode, EncSaveCardURL=arguments.EncSaveCardURL, strPrefill=local.strPrefill)>
	</cffunction>

	<cffunction name="showPaymentProfileForm" access="private" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="websiteOrgcode" type="string" required="yes">
		<cfargument name="EncSaveCardURL" type="string" required="yes">
		<cfargument name="strPrefill" type="struct" required="yes">

		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.html=''>
		<cfset local.returnStruct.head=''>
		
		<!--- validation js --->
		<cfsavecontent variable="local.returnStruct.head">
			<cfoutput>
			<style type="text/css">
			.alert { background:##fff6bf url(/media/exclamation.png) 15px center no-repeat; text-align:left; padding:5px 20px 5px 45px; border-top:2px solid ##f00; border-bottom:2px solid ##f00; } 

			input[type=text], input[type=password], select { margin-top: 4px; margin-bottom: 0px; }
			input[type=text], input[type=password] { border: 1px solid ##7F9DB9; padding: 2px; }
			select { border: 1px solid ##7F9DB9; }
			input[type=text][disabled=disabled] { background-color: ##EBEBE4; }
			input.Disabled { background-color: ##EBEBE4; }
			.FieldGroupSeparator { background-color: ##e0e0e0; font-weight: bold; padding: 5px 10px; margin-bottom: 10px; }
			.FieldGroupSeparatorBillingInfo { margin-top: 15px; }
			.PaymentPadlock { position:absolute; top: 15px; right: 10px; width: 13px; height: 15px; background-image: url('/media/padlock.png'); }
			.Comment { font-size: 11px; }
			.EditButtons { background-color: ##e0e0e0; padding: 5px; text-align: center; margin-top: 10px; }
			.disclaimer { padding-left:5px; margin-top:20px; }
			.disclaimer img { padding-right:8px; }
			.FieldGroupSeparatorPaymentInfo { display: block; }
			.PaymentItemEditData { padding: 4px; }
			.CreditCardInfo .DataLabelEdit,.AddressEdit .DataLabelEdit { display: inline-block; text-align: right; width: 134px; }
			.DataLabelEdit, .DataLabelEdit2 { font-weight: bold; }
			.DataValEditCardNum input { width: 150px; }
			.DataValEditExpDate input { width: 70px; }
			.DataValEditFirstName input { width: 200px; }
			.DataValEditLastName input { width: 200px; }
			.DataValEditStreet input { width: 200px; }
			.DataValEditCity input { width: 200px; }
			.DataValEditState input { width: 55px; }
			.DataValEditZip input { width: 75px; }
			.DataValEditCountry input { width: 200px; }
			</style>
			<script language="javascript">
				function hideAlert() { $('##everr').html('').hide(); };
				function showAlert(msg) { $('##everr').html(msg).attr('class','alert').show(); };
	
				function cancelIt() { 
					try { top.p__refresh(); } catch(e) { }
					var obj = new Object();
						obj.a = 'cancel';
					if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(obj);
				}
				function _FB_hasValue(obj, obj_type) {
					if (obj_type == 'TEXT' || obj_type == 'TEXTAREA'){
						tmp = obj.value;
						tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
						if (tmp.length == 0) return false;
						else return true;
					} else if (obj_type == 'SELECT'){
						for (var i=0; i < obj.length; i++) {
							if (obj.options[i].selected){
								tmp = obj.options[i].value;
								tmp = tmp.replace(/^\s+/,'').replace(/\s+$/,'');
								if (tmp.length > 0) return true;
							}
						}
						return false;	
					} else if (obj_type == 'SINGLE_VALUE_RADIO' || obj_type == 'SINGLE_VALUE_CHECKBOX'){
						if (obj.checked) return true;
						else return false;	
					} else if (obj_type == 'RADIO' || obj_type == 'CHECKBOX'){
						if (obj.length == undefined && obj.checked) return true;
						else{
							for (var i=0; i < obj.length; i++){
								if (obj[i].checked) return true;
							}
						}
						return false;
					}else{
						return true;
					}
				}
				function valExpDate() {
					var bad = false;
					<cfif len(arguments.strPrefill.fld_4_)>if ($('##fld_6_').val() != 'XXXX') {</cfif>
						var ed = $('##fld_6_').val().split(/[\/\-\s]+/);
						var pattern = /^\d+$/;
						if (!pattern.test(ed[0]) || !pattern.test(ed[1])) bad = true;
						if (ed[0] < 1 || ed[0] > 12 || ed[1] < 10 || ed[1] > 99) bad = true;
						if (!bad) $('##fld_6_').val(ed[0] + '/' + ed[1]);
					<cfif len(arguments.strPrefill.fld_4_)>}</cfif>
					return bad;
				}
				function valCardForm() {
					hideAlert();
					var arrReq = new Array();
					var thisForm = document.forms["frmAuthorizeCIM"];
					
					<cfif len(arguments.strPrefill.fld_4_)>if (thisForm['fld_4_'].value.indexOf("XXXX") < 0) {</cfif>
						if (!_CF_checkcreditcard(thisForm['fld_4_'].value, true)) arrReq[arrReq.length] = 'Card Number must be valid.';
					<cfif len(arguments.strPrefill.fld_4_)>}</cfif>
					if (!_FB_hasValue(thisForm['fld_6_'], 'TEXT')) arrReq[arrReq.length] = 'Expiration Date must be valid.';
					else if (valExpDate()) arrReq[arrReq.length] = 'Expiration Date must be valid.';

					if (arrReq.length == 0) {
						if (!_FB_hasValue(thisForm['fld_1_'], 'TEXT')) arrReq[arrReq.length] = 'First Name on Card cannot be blank.';
						if (!_FB_hasValue(thisForm['fld_2_'], 'TEXT')) arrReq[arrReq.length] = 'Last Name on Card cannot be blank.';
						if (!_FB_hasValue(thisForm['fld_11_'], 'TEXT')) arrReq[arrReq.length] = 'Billing Postal Code cannot be blank.';
					}
					
					if (arrReq.length > 0) {
						var msg = '';
						for (var i=0; i < arrReq.length; i++) msg += arrReq[i] + '<br/>';
						showAlert(msg);
						return false;
					}
					return true;
				}
			</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfsavecontent variable="local.returnStruct.html">
			<cfoutput>
			<cfset local.formURL = "/scheduled/buyNowCIM.cfm?nobanner=1&wizard=#arguments.EncSaveCardURL#">
			<cfform name="frmAuthorizeCIM" method="post" action="#local.formURL#" onsubmit="return valCardForm();">
				
			<div class="tsAppBodyText">
			<div class=PaymentItemEditData>
				<div class="FieldGroupSeparator FieldGroupSeparatorPaymentInfo">Credit Card Information</div>
				<div class=CreditCardInfo>
					<div><span class="DataLabelEdit">Card Number:&nbsp;</span> <span class="DataValEditCardNum"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_4_" name="fld_4_" maxLength="16" type="text" value="#arguments.strPrefill.fld_4_#"> * </span></div>
					<div><span class="DataLabelEdit">Expiration Date:&nbsp;</span> <span class="DataValEditExpDate"><cfinput autocomplete="off" class="tsAppBodyText" onkeypress="hideAlert();" id="fld_6_" name="fld_6_" maxLength="5" type="text" value="#arguments.strPrefill.fld_6_#"> * <span class="Comment">(mm/yy)</span></span></div>
				</div>
				<div class="FieldGroupSeparator FieldGroupSeparatorBillingInfo">Billing Information</div>
				<div class="AddressEdit">
					<div><span class="DataLabelEdit">First Name on Card:&nbsp;</span> <span class="DataValEditFirstName"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_1_" name="fld_1_" maxLength="50" type="text" value="#arguments.strPrefill.fld_1_#"></span> * </div>
					<div><span class="DataLabelEdit">Last Name on Card:&nbsp;</span> <span class="DataValEditLastName"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_2_" name="fld_2_" maxLength="50" type="text" value="#arguments.strPrefill.fld_2_#"></span> * </div>
					<div><span class="DataLabelEdit">Address:&nbsp;</span> <span class="DataValEditStreet"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_12_" name="fld_12_" maxLength="60" type="text" value="#arguments.strPrefill.fld_12_#"></span> </div>
					<div><span class="DataLabelEdit">City:&nbsp;</span> <span class="DataValEditCity"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_13_" name="fld_13_" maxLength="40" type="text" value="#arguments.strPrefill.fld_13_#"></span> </div>
					<div><span class="DataLabelEdit">State:&nbsp;</span> <span class="DataValEditState"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_14_" name="fld_14_" maxLength="40" type="text" value="#arguments.strPrefill.fld_14_#"></span> <span class="DataLabelEdit2">&nbsp; Postal Code:&nbsp;</span> <span class="DataValEditZip"><cfinput autocomplete="off" onkeypress="hideAlert();" class="tsAppBodyText" id="fld_11_" name="fld_11_" maxLength="20" type="text" value="#arguments.strPrefill.fld_11_#"></span> * </div>
				</div>
			</div>
			<div id="everr" style="display:none;margin:6px 0;"></div>
			<div class=EditButtons><button class="tsAppBodyButton" type="submit">Save</button> <button class="tsAppBodyButton" onclick="cancelIt()" type="button">Cancel</button>
				<cfif variables.x_testmode is 1><span class="tmode">&nbsp;** TEST MODE ** </span></cfif>			
			</div>
			<div class="disclaimer"><img src="/media/padlock.png" width="32" height="32" align="left"> Your payment information is safe & secure and will be processed in accordance with PCI credit card security standards.</div>
			</div>
			</cfform>
			</cfoutput>
		</cfsavecontent>		
		
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="insertPaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="tokenArgs" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.html=''>
		<cfset local.returnStruct.head=''>

		<!--- create customer profile id if it is blank/invalid (if we just added 1st card on file, for example) --->
		<cfif NOT isValidCustomerProfile(merchantOrgcode=arguments.merchantOrgcode, customerProfileID=arguments.customerProfileID)>
			<cfset local.strCustProfile = createCustomerProfile(merchantorgcode=arguments.merchantorgcode, customerID=arguments.customerID)>
			<cfset arguments.customerProfileid = local.strCustProfile.customerProfileID>
			<cfif len(local.strCustProfile.head)>
				<cfset local.returnStruct.head = local.strCustProfile.head>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>

		<!--- Ensure card number is valid formed --->
		<cfset arguments.tokenArgs.fld_4_ = rereplace(arguments.tokenArgs.fld_4_,"[^0-9]","","ALL")>
		<cfif NOT len(arguments.tokenArgs.fld_4_) or NOT isValid("creditcard",arguments.tokenArgs.fld_4_)>
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'There was an error saving the credit card. Invalid Card Number.';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfif>
		
		<!--- Ensure expiration can be made into proper format --->
		<cftry>
			<cfset local.expirationDate = "20" & GetToken(arguments.tokenArgs.fld_6_,2,'/') & "-" & numberformat(GetToken(arguments.tokenArgs.fld_6_,1,'/'),"09")>
			<cfif len(local.expirationDate) is not 7>
				<cfthrow>
			</cfif>
		<cfcatch type="Any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = 'There was an error saving the credit card. Invalid Expiration Date.';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
			<cfreturn local.returnStruct>
		</cfcatch>
		</cftry>
		
		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>

		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<createCustomerPaymentProfileRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#local.qryGetCIMInfo.authUsername#</name>
						<transactionKey>#local.qryGetCIMInfo.authTransKey#</transactionKey>
					</merchantAuthentication>
					<customerProfileId>#XMLFormat(arguments.customerProfileid)#</customerProfileId>
					<paymentProfile>
						<billTo>
							<firstName>#XMLFormat(left(arguments.tokenArgs.fld_1_,50))#</firstName>
							<lastName>#XMLFormat(left(arguments.tokenArgs.fld_2_,50))#</lastName>
							<cfif StructKeyExists(arguments.tokenArgs,"fld_12_")><address>#XMLFormat(left(arguments.tokenArgs.fld_12_,60))#</address></cfif>
							<cfif StructKeyExists(arguments.tokenArgs,"fld_13_")><city>#XMLFormat(left(arguments.tokenArgs.fld_13_,40))#</city></cfif>
							<cfif StructKeyExists(arguments.tokenArgs,"fld_14_")><state>#XMLFormat(left(arguments.tokenArgs.fld_14_,40))#</state></cfif>
							<zip>#XMLFormat(left(arguments.tokenArgs.fld_11_,20))#</zip>
						</billTo>
						<payment>
							<creditCard>
								<cardNumber>#arguments.tokenArgs.fld_4_#</cardNumber>
								<expirationDate>#local.expirationDate#</expirationDate>
							</creditCard>
						</payment>
					</paymentProfile>
					<validationMode>none</validationMode>
				</createCustomerPaymentProfileRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfif local.apiResponse.responseCode is 1>
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.a = 'save';
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
			<cfelse>
				<!--- need to catch if profile could not be created because it already exists --->
				<cfif findNoCase("[E00039]",local.apiResponse.responseReasonText)>
					<cfset refreshPaymentProfiles(merchantOrgcode=arguments.merchantOrgcode, customerProfileID=arguments.customerProfileid, customerID=arguments.customerID)>
					<cfthrow message="We couldn't save that credit card because it looks like it already exists on the account.">
				<cfelse>
					<cfthrow message="There was an error saving the credit card. #local.apiResponse.responseReasonText#">
				</cfif>
			</cfif>
			
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="updatePaymentProfile" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerPaymentProfileId" type="string" required="yes">
		<cfargument name="tokenArgs" type="struct" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.html=''>
		<cfset local.returnStruct.head=''>

		<!--- Ensure card number is valid formed --->
		<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_4_)>
			<cfset arguments.tokenArgs.fld_4_ = rereplace(arguments.tokenArgs.fld_4_,"[^0-9]","","ALL")>
			<cfif NOT len(arguments.tokenArgs.fld_4_) or NOT isValid("creditcard",arguments.tokenArgs.fld_4_)>
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'There was an error saving the credit card. Invalid Card Number.';
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.returnStruct>
			</cfif>
		</cfif>
		
		<!--- Ensure expiration can be made into proper format --->
		<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_6_)>
			<cftry>
				<cfset local.expirationDate = "20" & GetToken(arguments.tokenArgs.fld_6_,2,'/') & "-" & numberformat(GetToken(arguments.tokenArgs.fld_6_,1,'/'),"09")>
				<cfif len(local.expirationDate) is not 7>
					<cfthrow>
				</cfif>
			<cfcatch type="Any">
				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.err = 'There was an error saving the credit card. Invalid Expiration Date.';
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
				<cfreturn local.returnStruct>
			</cfcatch>
			</cftry>
		<cfelse>
			<cfset local.expirationDate = arguments.tokenArgs.fld_6_>
		</cfif>
		
		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>

		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<updateCustomerPaymentProfileRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#local.qryGetCIMInfo.authUsername#</name>
						<transactionKey>#local.qryGetCIMInfo.authTransKey#</transactionKey>
					</merchantAuthentication>
					<customerProfileId>#XMLFormat(arguments.customerProfileid)#</customerProfileId>
					<paymentProfile>
						<billTo>
							<firstName>#XMLFormat(left(arguments.tokenArgs.fld_1_,50))#</firstName>
							<lastName>#XMLFormat(left(arguments.tokenArgs.fld_2_,50))#</lastName>
							<cfif StructKeyExists(arguments.tokenArgs,"fld_12_")><address>#XMLFormat(left(arguments.tokenArgs.fld_12_,60))#</address></cfif>
							<cfif StructKeyExists(arguments.tokenArgs,"fld_13_")><city>#XMLFormat(left(arguments.tokenArgs.fld_13_,40))#</city></cfif>
							<cfif StructKeyExists(arguments.tokenArgs,"fld_14_")><state>#XMLFormat(left(arguments.tokenArgs.fld_14_,40))#</state></cfif>
							<zip>#XMLFormat(left(arguments.tokenArgs.fld_11_,20))#</zip>
						</billTo>
						<payment>
							<creditCard>
								<cardNumber>#XMLFormat(arguments.tokenArgs.fld_4_)#</cardNumber>
								<expirationDate>#XMLFormat(local.expirationDate)#</expirationDate>
							</creditCard>
						</payment>
						<customerPaymentProfileId>#XMLFormat(arguments.customerPaymentProfileid)#</customerPaymentProfileId>
					</paymentProfile>
					<validationMode>none</validationMode>
				</updateCustomerPaymentProfileRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfif local.apiResponse.responseCode is 1>
				<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_6_)>
					<cfset local.expMonth = numberformat(GetToken(arguments.tokenArgs.fld_6_,1,'/'),"09")>
					<cfset local.expYear = "20#GetToken(arguments.tokenArgs.fld_6_,2,'/')#">
					<cfset local.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>
				</cfif>

				<cfquery name="local.qryUpdateCC" datasource="#application.settings.dsn.trialsmith.dsn#">
					SET NOCOUNT ON;

					DECLARE @orgcode VARCHAR(10), @customerProfileID VARCHAR(50), @paymentProfileID VARCHAR(50), @detail VARCHAR(8);
					SET @orgcode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.merchantOrgcode#">;
					SET @customerProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerProfileid#">;
					SET @paymentProfileID = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.customerPaymentProfileid#">;
					SET @detail = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="XXXX#right(arguments.tokenArgs.fld_4_,4)#">;

					UPDATE dbo.ccMemberPaymentProfiles
					SET declined = 0,
						detail = @detail,
						<cfif NOT FindNoCase("XXXX",arguments.tokenArgs.fld_6_)>
							expiration = <cfqueryparam cfsqltype="CF_SQL_DATE" value="#local.expDate#">,
						</cfif>
						checkedForZIP = 1,
						hasZIP = <cfif StructKeyExists(arguments.tokenArgs,"fld_11_") and len(arguments.tokenArgs.fld_11_)>1<cfelse>0</cfif>
					WHERE paymentProfileID = @paymentProfileID 
					AND customerProfileID = @customerProfileID
					AND orgcode = @orgcode;
				</cfquery>

				<cfsavecontent variable="local.returnStruct.head">
					<cfoutput>
					<script language="javascript">
						$(function() {
							var objErr = new Object();
								objErr.a = 'save';
							if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
						});
					</script>
					</cfoutput>
				</cfsavecontent>
			<cfelse>
				<cfthrow message="There was an error saving the credit card. #local.apiResponse.responseReasonText#">
			</cfif>
			
		<cfcatch type="any">
			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				<script language="javascript">
					$(function() {
						var objErr = new Object();
							objErr.err = '#JSStringFormat(cfcatch.message)#';
						if (parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn) parent.p_#arguments.merchantOrgcode#_addPaymentProfileReturn(objErr);
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>
	
	<cffunction name="refreshPaymentProfiles" access="private" returntype="void" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		
		<cfset var local = structNew()>
		<cfset local.arrPayProfiles = arrayNew(1)>
		
		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>

		<cftry>
			<cfsavecontent variable="local.apiRequestBody">
				<cfoutput>{
					"getCustomerProfileRequest": { 
						"merchantAuthentication": { 
							"name": "#local.qryGetCIMInfo.authUsername#", 
							"transactionKey": "#local.qryGetCIMInfo.authTransKey#" 
						},
						"customerProfileId": "#arguments.customerProfileId#",
						"unmaskExpirationDate": true
					}
				}</cfoutput>
			</cfsavecontent>
			<cfset local.apiRequestBody = REReplace(trim(local.apiRequestBody), "[#chr(9)##chr(10)##chr(13)#]", "", "all")>

			<cfset local.strAuthorize = callAuthorize(apiRequestBody=local.apiRequestBody)>
			
			<cfif arrayLen(local.strAuthorize.arrErrors)>
				<!--- need to catch if profile was not found --->
				<cfif findNoCase("E00040",local.strAuthorize.rawAPIResponse)>
					<!--- do nothing. clear our profiles --->
				<cfelse>
					<cfthrow message="Error retrieving customer profile #arguments.customerProfileId#.">
				</cfif>
			<cfelse>
				<cfif local.strAuthorize.strAPIResponse.profile.keyExists("paymentProfiles")>
					<cfloop array="#local.strAuthorize.strAPIResponse.profile.paymentProfiles#" index="local.thisPaymentProfile">
						<cfset local.strProfile = { cpid=arguments.customerProfileId, ppid=local.thisPaymentProfile.customerPaymentProfileId, 
							detail=local.thisPaymentProfile.payment.creditcard.cardnumber, haszip=0, expDate="" }>
						<cfif structKeyExists(local.thisPaymentProfile, "billTo") and structKeyExists(local.thisPaymentProfile.billTo, "zip") and len(local.thisPaymentProfile.billTo.zip)>
							<cfset local.strProfile.haszip = 1>
						</cfif>
						<cfif structKeyExists(local.thisPaymentProfile.payment.creditcard, "expirationDate") and len(local.thisPaymentProfile.payment.creditcard.expirationDate)>
							<cfset local.expMonth = numberformat(GetToken(local.thisPaymentProfile.payment.creditcard.expirationDate,2,'-'),"09")>
							<cfset local.expYear = GetToken(local.thisPaymentProfile.payment.creditcard.expirationDate,1,'-')>
							<cfset local.strProfile.expDate = DateAdd("d",-1,DateAdd("m",1,"#local.expMonth#/1/#local.expYear#"))>
						</cfif>
						<cfset arrayAppend(local.arrPayProfiles,local.strProfile)>
					</cfloop>
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfsilent><cf_tlaException cfcatch="#cfcatch#" objectToDump="#local#"></cfsilent>
			<cfset local.arrPayProfiles = arrayNew(1)>
		</cfcatch>
		</cftry>
		
		<cfif ArrayLen(local.arrPayProfiles)>
			<cfquery name="local.qryUpdateMPP" datasource="#application.settings.dsn.trialsmith.dsn#">
				SET XACT_ABORT, NOCOUNT ON;
				BEGIN TRY
				
					declare @orgcode varchar(10), @customerProfileID varchar(50);
					declare @tblPP TABLE (cpid varchar(50), ppid varchar(50), detail varchar(50), expiration date, haszip bit);

					select @orgcode = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">;
					select @customerProfileID = <cfqueryparam value="#arguments.customerProfileID#" cfsqltype="CF_SQL_VARCHAR">;
				
					BEGIN TRAN
						<cfloop from="1" to="#ArrayLen(local.arrPayProfiles)#" index="local.pp">
							INSERT INTO @tblPP (cpid, ppid, detail, expiration, haszip)
							VALUES (
								<cfqueryparam value="#local.arrPayProfiles[local.pp].cpid#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.arrPayProfiles[local.pp].ppid#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.arrPayProfiles[local.pp].detail#" cfsqltype="CF_SQL_VARCHAR">,
								<cfif len(local.arrPayProfiles[local.pp].expDate)>
									<cfqueryparam value="#local.arrPayProfiles[local.pp].expDate#" cfsqltype="CF_SQL_DATE">,
								<cfelse>
									NULL,
								</cfif>
								<cfqueryparam value="#local.arrPayProfiles[local.pp].haszip#" cfsqltype="CF_SQL_BIT">
							);
						</cfloop>

						delete from dbo.ccMemberPaymentProfiles
						where customerProfileID = @customerProfileID
						and orgcode = @orgcode
						and not exists (
							select cpid
							from @tblPP
							where cpid = ccMemberPaymentProfiles.customerProfileID
							and ppid = ccMemberPaymentProfiles.paymentProfileID
							and detail = ccMemberPaymentProfiles.detail
						);
						
						update mpp
						set mpp.declined = 0,
							mpp.expiration = tbl.expiration
						from dbo.ccMemberPaymentProfiles as mpp
						inner join @tblPP as tbl on tbl.ppid = mpp.paymentProfileID and tbl.cpid = mpp.customerProfileID and tbl.detail = mpp.detail
						where mpp.orgcode = @orgcode;

						insert into dbo.ccMemberPaymentProfiles (customerid, depomemberdataid, orgcode, detail, expiration, customerProfileID, paymentProfileID, 
							dateAdded, declined, cardType, checkedForZIP, hasZIP)
						select '#arguments.customerid#', <cfif getToken(arguments.customerid,1,'_') eq "olddid">#getToken(arguments.customerid,2,'_')#<cfelse>null</cfif>, 
							@orgcode, detail, expiration, cpid, ppid, getdate(), 0, '', 1, haszip
						from @tblPP as tbl
						where not exists (
							select payProfileID
							from dbo.ccMemberPaymentProfiles
							where orgcode = @orgcode
							and customerProfileID = tbl.cpid
							and paymentProfileID = tbl.ppid
						);

						IF NOT EXISTS (
							SELECT TOP 1 payProfileID
							FROM dbo.ccMemberPaymentProfiles
							WHERE customerProfileID = @customerProfileID
						) BEGIN
							
							DECLARE @gatewayUsername varchar(50), @gatewayPassword varchar(75);
							SET @gatewayUsername = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryGetCIMInfo.authUsername#">;
							SET @gatewayPassword = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryGetCIMInfo.authTransKey#">;

							EXEC platformQueue.dbo.queue_authCIMCustCheck_load @profileID=0, @gatewayUsername=@gatewayUsername, 
								@gatewayPassword=@gatewayPassword, @customerProfileID=@customerProfileID;
						END
					COMMIT TRAN

					SELECT 1 AS success;
				
				END TRY
				BEGIN CATCH
					IF @@trancount > 0 ROLLBACK TRANSACTION;
					EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
				END CATCH
			</cfquery>
			
			<cfif getToken(arguments.customerid,1,'_') eq "olddid">
				<cfquery name="local.qryinsertNote" datasource="#application.settings.dsn.trialsmith.dsn#">
					insert into CustomerNotes (depomemberdataid, NoteTypeID, Note)
					values(
						<cfqueryparam value="#getToken(arguments.customerid,2,'_')#" cfsqltype="CF_SQL_INTEGER">,
						1,
						<cfqueryparam value="#arguments.merchantOrgcode# Credit Card Refreshed" cfsqltype="CF_SQL_VARCHAR">
					)
				</cfquery>
			</cfif>
		</cfif>
	</cffunction>	
	
	<cffunction name="addPaymentProfileReturn" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		<cfargument name="customerID" type="string" required="yes">
		<cfargument name="websiteOrgcode" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = structNew()>
		<cfset local.returnStruct.head = ''>

		<cfset local.customerProfileId = arguments.customerProfileId>
		<cfif NOT len(local.customerProfileId)>
			<cfset local.strCustProfile = createCustomerProfile(merchantOrgcode=arguments.merchantOrgcode, customerID=arguments.customerID)>
			<cfset local.customerProfileid = local.strCustProfile.customerProfileID>
			<cfif len(local.strCustProfile.head)>
				<cfset local.returnStruct.head = local.strCustProfile.head>
			</cfif>
		</cfif>

		<!--- get customer profile --->
		<cfif len(local.customerProfileId)>
			<cfset refreshPaymentProfiles(merchantOrgcode=arguments.merchantOrgcode, customerProfileID=local.customerProfileid, customerID=arguments.customerID)>

			<!--- get the gather form to update the calling page --->
			<cfset local.strGather = gather(merchantOrgcode=arguments.merchantOrgcode, customerID=arguments.customerID, websiteorgcode=arguments.websiteorgcode)>

			<cfsavecontent variable="local.returnStruct.head">
				<cfoutput>
				#local.returnStruct.head#
				<script language="javascript">
					$(function() {
						if (parent.p_#arguments.merchantOrgcode#_refresh) parent.p_#arguments.merchantOrgcode#_refresh();
						else parent.$('##p_#arguments.merchantOrgcode#_DIV').parent().html('#JSStringFormat(local.strGather.inputForm)#');
						parent.$('input.appContinueBtn').removeAttr("disabled");
						parent.$('##caserefTable').show();
					});
				</script>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="chargeCard" access="public" returntype="struct" output="no">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="payProfileID" type="numeric" required="yes">
		<cfargument name="amount" type="numeric" required="yes">
		<cfargument name="detail" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.rawResponse=''>
		<cfset local.returnStruct.responseCode=99999>
		<cfset local.returnStruct.responseReasonText='Invalid request.'>
		<cfset local.returnStruct.responseReasonCode=''>
		<cfset local.returnStruct.cardType=''>
		<cfset local.returnStruct.authorizationCode=''>

		<cfquery name="local.qrygetCard" datasource="#application.settings.dsn.trialsmith.dsn#">
			select customerid, depomemberdataid, detail, customerProfileID, paymentProfileID, orgcode
			from ccMemberPaymentProfiles 
			where payProfileID = <cfqueryparam value="#arguments.payProfileID#" cfsqltype="CF_SQL_INTEGER">
			and declined = 0
		</cfquery>

		<!--- create request --->
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<createCustomerProfileTransactionRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#arguments.CIMUsername#</name>
						<transactionKey>#arguments.CIMPassword#</transactionKey>
					</merchantAuthentication>
					<transaction>
						<profileTransAuthCapture>
							<amount>#arguments.amount#</amount>
							<customerProfileId>#local.qrygetCard.customerProfileID#</customerProfileId>
							<customerPaymentProfileId>#local.qrygetCard.paymentProfileID#</customerPaymentProfileId>
							<order>
								<description>#xmlformat(arguments.detail)#</description>
							</order>
							<recurringBilling>false</recurringBilling>
						</profileTransAuthCapture>
					</transaction>
					<extraOptions><![CDATA[ x_delim_data=true&x_delim_char='|'&x_solution_id=#variables.x_solution_id# ]]></extraOptions>
				</createCustomerProfileTransactionRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfset local.returnStruct.rawResponse = local.apiResponse.rawResponse>
			<cfset local.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset local.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset local.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>
			<cfset local.returnStruct.transactionID = local.apiResponse.transactionID>
			<cfset local.returnStruct.cardType = left(local.apiResponse.cardType,1)>
			<cfset local.returnStruct.authorizationCode = local.apiResponse.authorizationCode>
			
		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = cfcatch.message>
		</cfcatch>
		</cftry>
		
		<cfif local.qrygetCard.depomemberdataid gt 0 or left(local.qrygetCard.customerid,7) eq "oldjid_">
			<cfif local.returnStruct.responseCode is 1>
				<cfset local.PaymentDescription = "Credit Card Payment - #local.qrygetCard.detail#">
			<cfelse>
				<cfset local.PaymentDescription = "Credit Card Declined - #local.qrygetCard.detail# - $#arguments.amount# - #local.returnStruct.responseReasonText#">
			</cfif>

			<cfif local.qrygetCard.depomemberdataid gt 0>
				<cfquery name="local.qryInsertTrans" datasource="#application.settings.dsn.trialsmith.dsn#">
					insert into dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid, 
						sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
						merchantOrgCode, refundableAmount)
					select <cfqueryparam value="#local.PaymentDescription#" cfsqltype="CF_SQL_VARCHAR">, 
						<cfif local.returnStruct.responseCode is 1>
							<cfqueryparam value="#arguments.amount * -1#" cfsqltype="CF_SQL_DOUBLE">,
							0,
						<cfelse>
							0,
							0,
						</cfif>
						getdate(), depomemberdataid, tlamemberstate, 
						<cfqueryparam value="#local.returnStruct.authorizationCode#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#local.returnStruct.cardType#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#local.returnStruct.transactionID#" cfsqltype="CF_SQL_VARCHAR">,
						<cfqueryparam value="#local.returnStruct.responseCode#" cfsqltype="CF_SQL_INTEGER">,
						<cfqueryparam value="#local.returnStruct.responseReasonCode#" cfsqltype="CF_SQL_INTEGER">, 1,
						<cfqueryparam value="#local.qrygetCard.orgcode#" cfsqltype="CF_SQL_VARCHAR">,
						<cfif local.returnStruct.responseCode is 1>
							<cfqueryparam value="#arguments.amount#" cfsqltype="CF_SQL_DOUBLE">
						<cfelse>
							null
						</cfif>
					from dbo.depomemberdata
					where depomemberdataid = <cfqueryparam value="#local.qrygetCard.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
				</cfquery>
			<cfelse>
				<cfset local.returnStruct.strInsertTrans = structNew()>
				<cfset local.returnStruct.strInsertTrans.description = local.PaymentDescription>
				<cfif local.returnStruct.responseCode is 1>
					<cfset local.returnStruct.strInsertTrans.AmountBilled = arguments.amount * -1>
				<cfelse>
					<cfset local.returnStruct.strInsertTrans.AmountBilled = 0>
				</cfif>
				<cfset local.returnStruct.strInsertTrans.approvalCode = local.returnStruct.authorizationCode>
				<cfset local.returnStruct.strInsertTrans.paymentmethod = local.returnStruct.cardType>
				<cfset local.returnStruct.strInsertTrans.ccTransactionID = local.returnStruct.transactionID>
				<cfset local.returnStruct.strInsertTrans.ccResponseCode = local.returnStruct.responseCode>
				<cfset local.returnStruct.strInsertTrans.ccResponseReasonCode = local.returnStruct.responseReasonCode>
				<cfset local.returnStruct.strInsertTrans.payProfileID = arguments.payProfileID>
			</cfif>

		</cfif>

		<!--- TS-17, Don't mark duplicate transaction failures as declined --->
		<cfquery name="local.qrySetDeclined" datasource="#application.settings.dsn.trialsmith.dsn#">
			update dbo.ccMemberPaymentProfiles 
			set declined = <cfif (local.returnStruct.responseCode is 1)	OR 
													(local.returnStruct.responseCode is 3 AND FindNoCase("A duplicate transaction has been submitted",local.returnStruct.responseReasonText))>0<cfelse>1</cfif>
				<cfif len(local.returnStruct.cardType)>, cardType = <cfqueryparam value="#local.returnStruct.cardType#" cfsqltype="CF_SQL_VARCHAR"></cfif>
			where payProfileID = <cfqueryparam value="#arguments.payProfileID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="chargeApplePay" access="public" returntype="struct" output="no">
		<cfargument name="merchantOrgCode" type="string" required="yes">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="depomemberdataid" type="numeric" required="yes">
		<cfargument name="tokenData" type="struct" required="yes">
		<cfargument name="detail" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.rawResponse=''>
		<cfset local.returnStruct.responseCode=99999>
		<cfset local.returnStruct.responseReasonText='Invalid request.'>
		<cfset local.returnStruct.responseReasonCode=''>
		<cfset local.returnStruct.cardType=''>
		<cfset local.returnStruct.authorizationCode=''>

		<!--- Convert date from Apple's YYMMDD to YYYY-MM --->
		<cfset local.expDate = "20#left(arguments.tokenData.decryptedToken.applicationExpirationDate,2)#-#mid(arguments.tokenData.decryptedToken.applicationExpirationDate, "3", "2")#">

		<!--- Convert amount from cents to dollars --->
		<cfset local.amount = (arguments.tokenData.decryptedToken.transactionAmount/100)>

		<!--- create request --->
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
					<createTransactionRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
						<merchantAuthentication>
							<name>#arguments.CIMUsername#</name>
							<transactionKey>#arguments.CIMPassword#</transactionKey>
						</merchantAuthentication>
						<transactionRequest>
						  <transactionType>authCaptureTransaction</transactionType>
						  <amount>#local.amount#</amount>
						  <payment>
							<creditCard>
								<cardNumber>#arguments.tokenData.decryptedToken.applicationPrimaryAccountNumber#</cardNumber>
								<expirationDate>#local.expDate#</expirationDate>
								<isPaymentToken>true</isPaymentToken>
								<cryptogram>#arguments.tokenData.decryptedToken.paymentdata.onlinePaymentCryptogram#</cryptogram>
							</creditCard>  
						  </payment>
						  <order>
							<description>#xmlformat(arguments.detail)#</description>
						  </order>
						  <retail>
							<marketType>0</marketType>
						  </retail>
						  <transactionSettings>
							<setting>
							  <settingName>recurringBilling</settingName>
							  <settingValue>false</settingValue>
							</setting>
						  </transactionSettings>
						</transactionRequest>
					  </createTransactionRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfset local.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset local.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset local.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>
			<cfset local.returnStruct.transactionID = local.apiResponse.transactionID>
			<cfset local.returnStruct.cardType = left(local.apiResponse.cardType,1)>
			<cfset local.returnStruct.authorizationCode = local.apiResponse.authorizationCode>

		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = cfcatch.message>
			<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		</cfcatch>
		</cftry>

		<cfif local.returnStruct.responseCode is 1>
			<cfset local.PaymentDescription = "Credit Card Payment (Apple Pay) - #arguments.tokenData.encryptedToken.paymentMethod.displayname#">

			<cfset local.rawResponse = xmlParse(local.apiResponse.rawResponse)>

			<cfset local.returnStruct.transactionID = XMLSearch(local.rawResponse,"/*/transactionResponse/transId/text()[1]")[1].XmlText>
			<cfset local.returnStruct.cardType = left(XMLSearch(local.rawResponse,"/*/transactionResponse/accountType/text()[1]")[1].XmlText,1)>
			<cfset local.returnStruct.authorizationCode = XMLSearch(local.rawResponse,"/*/transactionResponse/authCode/text()[1]")[1].XmlText>
		<cfelse>
			<cfset local.PaymentDescription = "Credit Card Declined (Apple Pay) - #arguments.tokenData.encryptedToken.paymentMethod.displayname# - $#local.amount# - #local.returnStruct.responseReasonText#">
		</cfif>

		<cfquery name="local.qryInsertTrans" datasource="#application.settings.dsn.trialsmith.dsn#">
			insert into dbo.depotransactions (description, AmountBilled, SalesTaxAmount, refundableAmount, datepurchased, depomemberdataid, 
				sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
				merchantOrgCode, isApplePay)
			select <cfqueryparam value="#local.PaymentDescription#" cfsqltype="CF_SQL_VARCHAR">, 
				<cfif local.returnStruct.responseCode is 1>
					<cfqueryparam value="#local.amount * -1#" cfsqltype="CF_SQL_DOUBLE">,
					0,
					<cfqueryparam value="#local.amount#" cfsqltype="CF_SQL_DOUBLE">,
				<cfelse>
					0,
					0,
					0,
				</cfif>
				getdate(), depomemberdataid, tlamemberstate, 
				<cfqueryparam value="#local.returnStruct.authorizationCode#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#local.returnStruct.cardType#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#local.returnStruct.transactionID#" cfsqltype="CF_SQL_VARCHAR">,
				<cfqueryparam value="#local.returnStruct.responseCode#" cfsqltype="CF_SQL_INTEGER">,
				<cfqueryparam value="#local.returnStruct.responseReasonCode#" cfsqltype="CF_SQL_INTEGER">, 
				1,
				<cfqueryparam value="#arguments.merchantOrgCode#" cfsqltype="CF_SQL_VARCHAR">,
				1
			from dbo.depomemberdata
			where depomemberdataid = <cfqueryparam value="#arguments.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="removeCard" access="public" returntype="struct" output="no">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="customerProfileID" type="numeric" required="yes">
		<cfargument name="paymentProfileID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.rawResponse=''>
		<cfset local.returnStruct.responseCode=99999>
		<cfset local.returnStruct.responseReasonText='Invalid request.'>
		<cfset local.returnStruct.responseReasonCode=''>

		<!--- create request --->
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<deleteCustomerPaymentProfileRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#arguments.CIMUsername#</name>
						<transactionKey>#arguments.CIMPassword#</transactionKey>
					</merchantAuthentication>
					<customerProfileId>#arguments.customerProfileID#</customerProfileId>
					<customerPaymentProfileId>#arguments.paymentProfileID#</customerPaymentProfileId>
				</deleteCustomerPaymentProfileRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfset local.returnStruct.rawResponse = local.apiResponse.rawResponse>
			<cfset local.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset local.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset local.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>

		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="credit" access="public" returntype="struct" output="no">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="transactionID" type="numeric" required="yes">
		<cfargument name="refundAmount" type="numeric" required="yes">
		<cfargument name="payProfileID" type="numeric" required="yes">
		<cfargument name="maskedCardNumber" type="string" required="no" default="">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { "success":false, "errors":[] }>
		<cfset local.arrReturnStructs = arrayNew(1)>
		<cfset local.attemptedVoid = false>
		<cfset local.doRefundTransaction = false>

		<cftry>
			<!--- if this is a partial refund, need to check for a valid payment profile used for the repayment --->
			<cfquery name="local.qryMemberProfile" datasource="#application.settings.dsn.trialsmith.dsn#">
				SELECT customerProfileID, paymentProfileID
				FROM dbo.ccMemberPaymentProfiles
				WHERE payProfileID = <cfqueryparam value="#arguments.payProfileID#" cfsqltype="CF_SQL_INTEGER">
				AND declined = 0
			</cfquery>

			<cfquery name="local.qryTransaction" datasource="#application.settings.dsn.trialsmith.dsn#">
				SELECT AmountBilled, Description as origPaymentDescription, ccTransactionID
				FROM dbo.depoTransactions
				WHERE transactionID = <cfqueryparam value="#arguments.transactionID#" cfsqltype="CF_SQL_INTEGER">
				AND AmountBilled < 0
			</cfquery>

			<cfset local.origPaymentAmount = local.qryTransaction.AmountBilled * -1>
			<cfset local.amountDifference = local.origPaymentAmount-arguments.refundAmount>

			<!--- if (partial refund and profile exists) OR full refund, try void first --->
			<cfif (local.amountDifference gt 0 AND local.qryMemberProfile.recordCount is 1) OR local.amountDifference eq 0>
				<cfset local.voidReturnStruct = { responseCode=99999, responseReasonText="Invalid request", responseReasonCode="", rawResponse="", transactionid="", cardType="", authorizationCode="" }>
			
				<cfset local.voidReturnStruct = voidTransaction(CIMUsername=arguments.CIMUsername, CIMPassword=arguments.CIMPassword, origTransactionID=arguments.transactionID)>
				<cfset local.attemptedVoid = true>
				
				<cfset local.strVoidResult = structNew()>
				<cfset local.strVoidResult.rawResponse = local.voidReturnStruct.rawResponse>
				<cfset local.strVoidResult.responseCode = local.voidReturnStruct.responseCode>
				<cfset local.strVoidResult.responseReasonText = local.voidReturnStruct.responseReasonText>
				<cfset local.strVoidResult.responseReasonCode = local.voidReturnStruct.responseReasonCode>
				<cfset local.strVoidResult.transactionid = local.voidReturnStruct.transactionid>
				<cfset local.strVoidResult.refundType = "void">
				<cfset local.strVoidResult.refundAmt = local.origPaymentAmount>
				<cfset arrayAppend(local.arrReturnStructs, local.strVoidResult)>

				
				<!--- if the void was a success, and it was a full refund --->
				<cfif local.strVoidResult.responseCode is 1 and local.amountDifference eq 0>
					<cfset local.returnStruct.success = true>

				<!--- if the void was a success, we need to charge the difference between original amount and refund amount --->
				<cfelseif local.strVoidResult.responseCode is 1 and local.amountDifference gt 0>
					<cfset local.chargeReturnStruct = { responseCode=99999, responseReasonText="Invalid request", responseReasonCode="", rawResponse="", transactionid="", cardType="", authorizationCode="" }>

					<cfset local.chargeReturnStruct = chargeCard(CIMUsername=arguments.CIMUsername, CIMPassword=arguments.CIMPassword,
						payProfileID=arguments.payProfileID, amount=local.amountDifference, detail=local.qryTransaction.origPaymentDescription)>
					
					<cfset local.strChargeResult = structNew()>
					<cfset local.strChargeResult.rawResponse = local.chargeReturnStruct.rawResponse>
					<cfset local.strChargeResult.responseCode = local.chargeReturnStruct.responseCode>
					<cfset local.strChargeResult.responseReasonText = local.chargeReturnStruct.responseReasonText>
					<cfset local.strChargeResult.responseReasonCode = local.chargeReturnStruct.responseReasonCode>
					<cfset local.strChargeResult.transactionid = local.chargeReturnStruct.transactionid>
					<cfset local.strChargeResult.cardType = local.chargeReturnStruct.cardType>
					<cfset local.strChargeResult.authorizationCode = local.chargeReturnStruct.authorizationCode>
					<cfset arrayAppend(local.arrReturnStructs, local.strChargeResult)>

					<cfif local.strChargeResult.responseCode is 1>
						<cfset local.returnStruct.success = true>
					<cfelse>
						<cfset arrayAppend(local.returnStruct.errors, { "msg":local.strChargeResult.responseReasonText, "errorCode":"chargeCard fail" })>
					</cfif>

				<!--- else if the void was not a success, try to refund --->
				<cfelseif local.strVoidResult.responseCode is not 1 and len(arguments.maskedCardNumber)>
					<cfset local.doRefundTransaction = true>
				</cfif>
			<cfelseif local.amountDifference gt 0 and len(arguments.maskedCardNumber)>
				<!--- this can be issued against a deleted customer profile --->
				<cfset local.doRefundTransaction = true>
			</cfif>

			<cfif local.doRefundTransaction>
				<cfset local.refundReturnStruct = { responseCode=99999, responseReasonText="Invalid request", responseReasonCode="", rawResponse="", transactionid="", cardType="", authorizationCode="" }>

				<cfset local.refundReturnStruct = refundTransaction(
					CIMUsername=arguments.CIMUsername, CIMPassword=arguments.CIMPassword, transactionID=arguments.transactionID,
					refundAmount=arguments.refundAmount, ccTransactionID=local.qryTransaction.ccTransactionID, creditCardNumberMasked=arguments.maskedCardNumber
				)>

				<cfset local.strRefundResult = structNew()>
				<cfset local.strRefundResult.rawResponse = local.refundReturnStruct.rawResponse>
				<cfset local.strRefundResult.responseCode = local.refundReturnStruct.responseCode>
				<cfset local.strRefundResult.responseReasonText = local.refundReturnStruct.responseReasonText>
				<cfset local.strRefundResult.responseReasonCode = local.refundReturnStruct.responseReasonCode>
				<cfset local.strRefundResult.transactionid = local.refundReturnStruct.transactionid>
				<cfset local.strRefundResult.cardType = local.refundReturnStruct.cardType>
				<cfset local.strRefundResult.authorizationCode = local.refundReturnStruct.authorizationCode>
				<cfset local.strRefundResult.refundType = "refund">
				<cfset local.strRefundResult.refundAmt = arguments.refundAmount>
				<cfset arrayAppend(local.arrReturnStructs, local.strRefundResult)>

				<cfif local.strRefundResult.responseCode is 1>
					<cfset local.returnStruct.success = true>
				<cfelse>
					<cfset arrayAppend(local.returnStruct.errors, { "msg":local.strRefundResult.responseReasonText, "errorCode": local.attemptedVoid ? "refund fail - post void attempt" : "refund fail - against trx with deleted customer profile" })>
				</cfif>
			</cfif>
			
			<cfcatch type="any">
				<cfset local.returnStruct.success = false>
				<cfset arrayAppend(local.returnStruct.errors, { "msg":cfcatch.message, "errorCode":"credit_catch" })>
			</cfcatch>
		</cftry>

		<cfset local.returnStruct.arrResponseStruct = local.arrReturnStructs>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="voidTransaction" access="public" returntype="struct" output="no">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="origTransactionID" type="numeric" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = StructNew()>
		<cfset local.returnStruct.rawResponse=''>
		<cfset local.returnStruct.responseCode=99999>
		<cfset local.returnStruct.responseReasonText='Invalid request.'>
		<cfset local.returnStruct.responseReasonCode=''>
		<cfset local.returnStruct.cardType=''>
		<cfset local.returnStruct.authorizationCode=''>

		<cfquery name="local.qryOrigTransaction" datasource="#application.settings.dsn.trialsmith.dsn#">
			select depoMemberDataID, ccTransactionID, AmountBilled, Description, merchantOrgCode, isApplePay, isGooglePay
			from dbo.depoTransactions
			where transactionID = <cfqueryparam value="#arguments.origTransactionID#" cfsqltype="CF_SQL_INTEGER">
		</cfquery>

		<!--- create request --->
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<createTransactionRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#arguments.CIMUsername#</name>
						<transactionKey>#arguments.CIMPassword#</transactionKey>
					</merchantAuthentication>
					<refId></refId>
					<transactionRequest>
						<transactionType>voidTransaction</transactionType>
						<refTransId>#local.qryOrigTransaction.ccTransactionID#</refTransId>
					</transactionRequest>
				</createTransactionRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfset local.returnStruct.rawResponse = local.apiResponse.rawResponse>
			<cfset local.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset local.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset local.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>
			<cfset local.returnStruct.transactionID = local.apiResponse.transactionID>
			<cfset local.returnStruct.cardType = left(local.apiResponse.cardType,1)>
			<cfset local.returnStruct.authorizationCode = local.apiResponse.authorizationCode>

			<cfif local.returnStruct.responseCode is 1>
				<cfquery name="local.qryInsertTrans" datasource="#application.settings.dsn.trialsmith.dsn#">
					SET XACT_ABORT, NOCOUNT ON;
					BEGIN TRY
						DECLARE @origTransactionID int = <cfqueryparam value="#arguments.origTransactionID#" cfsqltype="CF_SQL_INTEGER">;

						BEGIN TRAN;
							INSERT INTO dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depoMemberDataID, 
								sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
								Reversable, ReversedTransactionID, merchantOrgCode, isApplePay, isGooglePay)
							SELECT <cfqueryparam value="VOID - #local.qryOrigTransaction.Description#" cfsqltype="CF_SQL_VARCHAR">, 
								<cfqueryparam value="#local.qryOrigTransaction.AmountBilled * -1#" cfsqltype="CF_SQL_DOUBLE">,
								0,
								getdate(), depoMemberDataID, tlamemberstate, 
								<cfqueryparam value="#local.returnStruct.authorizationCode#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.returnStruct.cardType#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.returnStruct.transactionID#" cfsqltype="CF_SQL_VARCHAR">,
								<cfqueryparam value="#local.returnStruct.responseCode#" cfsqltype="CF_SQL_INTEGER">,
								<cfqueryparam value="#local.returnStruct.responseReasonCode#" cfsqltype="CF_SQL_INTEGER">, 1, 'N', @origTransactionID,
								NULLIF(<cfqueryparam value="#local.qryOrigTransaction.merchantOrgCode#" cfsqltype="CF_SQL_VARCHAR">,''),
								<cfqueryparam value="#local.qryOrigTransaction.isApplePay#" cfsqltype="CF_SQL_BIT">,
								<cfqueryparam value="#local.qryOrigTransaction.isGooglePay#" cfsqltype="CF_SQL_BIT">
							FROM dbo.depomemberdata
							WHERE depoMemberDataID = <cfqueryparam value="#local.qryOrigTransaction.depoMemberDataID#" cfsqltype="CF_SQL_INTEGER">;

							UPDATE dbo.depotransactions
							SET refundableAmount = 0
							WHERE transactionID = @origTransactionID;
						COMMIT TRAN;
					END TRY
					BEGIN CATCH
						IF @@trancount > 0 ROLLBACK TRANSACTION;
						EXEC membercentral.dbo.up_MCErrorHandler @raise=1, @email=0;
					END CATCH
				</cfquery>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="refundTransaction" access="public" returntype="struct" output="no">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="transactionID" type="numeric" required="yes">
		<cfargument name="refundAmount" type="numeric" required="yes">
		<cfargument name="ccTransactionID" type="numeric" required="yes">
		<cfargument name="creditCardNumberMasked" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { rawResponse='', responseCode=99999, responseReasonText='Invalid request.', responseReasonCode='', cardType='', authorizationCode='' }>

		<!--- create request --->
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<createTransactionRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#arguments.CIMUsername#</name>
						<transactionKey>#arguments.CIMPassword#</transactionKey>
					</merchantAuthentication>
					<refId></refId>
					<transactionRequest>
						<transactionType>refundTransaction</transactionType>
						<amount>#arguments.refundAmount#</amount>
						<payment>
							<creditCard>
								<cardNumber>#right(arguments.creditCardNumberMasked,4)#</cardNumber>
								<expirationDate>XXXX</expirationDate>
							</creditCard>
						</payment>
						<refTransId>#arguments.ccTransactionID#</refTransId>
					</transactionRequest>
				</createTransactionRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfset local.returnStruct.rawResponse = local.apiResponse.rawResponse>
			<cfset local.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset local.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset local.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>
			<cfset local.returnStruct.transactionID = local.apiResponse.transactionID>
			<cfset local.returnStruct.authorizationCode = local.apiResponse.authorizationCode>
			<cfset local.returnStruct.cardType = left(local.apiResponse.cardType,1)>

			<cfset local.recordTxnSuccess = recordRefundTransaction(originalTransactionID=arguments.transactionID, refundTransactionID=local.returnStruct.transactionID,
				refundAmount=arguments.refundAmount, refundSuccess=local.returnStruct.responseCode eq 1, errMsg=local.returnStruct.responseCode neq 1 ? local.returnStruct.responseReasonText : "")>
		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = cfcatch.message>
		</cfcatch>
		</cftry>

		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="recordRefundTransaction" access="public" returntype="boolean" output="no">
		<cfargument name="originalTransactionID" type="numeric" required="yes">
		<cfargument name="refundTransactionID" type="string" required="yes">
		<cfargument name="refundAmount" type="numeric" required="yes">
		<cfargument name="refundSuccess" type="boolean" required="yes">
		<cfargument name="errMsg" type="string" required="no" default="">

		<cfset var local = structNew()>
		<cfset local.success = true>
		
		<cftry>
			<cfquery name="local.qryInsertTrans" datasource="#application.settings.dsn.trialsmith.dsn#">
				SET NOCOUNT ON;

				DECLARE @depoMemberDataID int, @detail varchar(600), @amount decimal(18,2), @sourceState varchar(10), @paymentmethod char(2), 
					@merchantOrgCode varchar(10), @transactionID int, @refundTransactionID varchar(50), @refundableAmount decimal(18,2),
					@isApplePay bit = 0, @isGooglePay bit = 0;
				SET @amount = <cfqueryparam value="#NumberFormat(arguments.refundAmount,"0.00")#" cfsqltype="CF_SQL_DOUBLE">;
				SET @transactionID = <cfqueryparam value="#arguments.originalTransactionID#" cfsqltype="CF_SQL_INTEGER">;
				SET @refundTransactionID = NULLIF(<cfqueryparam value="#arguments.refundTransactionID#" cfsqltype="CF_SQL_VARCHAR">,'');

				SELECT @depoMemberDataID = depoMemberDataID, @detail = description, @sourcestate = sourcestate, @paymentmethod = paymentmethod,
					@merchantOrgCode = merchantOrgCode, @refundableAmount = isnull(refundableAmount,amountbilled*-1), @isApplePay = isApplePay,
					@isGooglePay = isGooglePay
				FROM dbo.depoTransactions
				WHERE transactionID = @transactionID
				AND isPayment = 1
				AND AmountBilled < 0;

				<cfif arguments.refundSuccess>
					SET @refundableAmount = @refundableAmount - @amount;
					SET @detail = 'REFUND - ' + @detail;
				<cfelse>
					SET @amount = 0;
					SET @refundableAmount = 0;
					SET @detail = LEFT('REFUND FAILED - ' + <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.errMsg#"> + ' - ' + @detail,600);
				</cfif>

				BEGIN TRAN;
					INSERT INTO dbo.depotransactions (depomemberdataid, description, amountbilled, datepurchased, sourcestate, reversable, 
						purchasecreditflag, paymentmethod, isPayment, ccTransactionID, merchantOrgCode, isApplePay, isGooglePay)
					VALUES (@depoMemberDataID, @detail, @amount, getdate(), @sourcestate, 'Y', 'N', @paymentmethod, 1,
						@refundTransactionID, @merchantOrgCode, @isApplePay, @isGooglePay);

					<cfif arguments.refundSuccess>
						UPDATE dbo.depotransactions
						SET refundableAmount = @refundableAmount
						WHERE transactionID = @transactionID;
					</cfif>
				COMMIT TRAN;
			</cfquery>
		<cfcatch type="any">
			<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="callGateway" access="private" returntype="struct" output="no">
		<cfargument name="xmlRequest" type="xml" required="yes">
	
		<cfset var local = StructNew()>
		<cfset local.response = StructNew()>
		<cfset local.response.rawResponse=''>
		<cfset local.response.responseCode=3>
		<cfset local.response.responseSubCode=0>
		<cfset local.response.responseReasonText=''>
		<cfset local.response.responseReasonCode=3>
		<cfset local.response.authorizationCode=''>
		<cfset local.response.avsResponse=''>
		<cfset local.response.transactionid=0>
		<cfset local.response.description=''>
		<cfset local.response.cardCodeResponse=''>
		<cfset local.response.accountNumber=''>
		<cfset local.response.cardType=''>

		<cfif variables.x_testmode is 1>
			<cfset local.AuthURL = "https://apitest.authorize.net/xml/v1/request.api">
		<cfelse>
			<cfset local.AuthURL = "https://api2.authorize.net/xml/v1/request.api">
		</cfif>

		<cftry>
			<cfhttp url="#local.AuthURL#" method="post" throwonerror="Yes" result="local.APIResult" charset="utf-8">
				<cfhttpparam type="xml" value="#arguments.xmlRequest#">
			</cfhttp>

			<!--- Remove BOM because the current version of InputStreamReader doesn't do that for you as it should. --->
			<cfset local.apiFileContent = local.APIResult.fileContent>
			<cfset local.apiFileContentPOS = find('<?xml version="1.0" encoding="utf-8"?>',local.apiFileContent)>
			<cfset local.apiFileContent = RemoveChars(local.apiFileContent,1,local.apiFileContentPOS-1)>

			<!--- namespaces in result prevent 'typical' xmlpath searching, so strip them out --->
			<cfset local.apiFileContent = local.apiFileContent.ReplaceAll("xmlns(:\w+)?=""[^""]*""","")>

			<!--- parse response to xml --->
			<cfset local.apixml = xmlParse(local.apiFileContent)>
			<cfset local.response.rawResponse = local.apixml>

			<!--- Check API response for errors --->
			<cfset local.resultCode = XMLSearch(local.apixml,"/*/messages/resultCode")>
			<cfif arrayLen(local.resultCode) is 1>
				<cfset local.resultCode = local.resultCode[1].xmlText>
			<cfelse>
				<cfset local.resultCode = "">
			</cfif>
			
			<!--- if "ok" the call was successful --->
			<cfif local.resultCode eq "Ok">
				
				<!--- if directResponse node exists, convert response to array. ensure 3rd argument is true to keep empty elements --->
				<cfset local.rawResponse = XMLSearch(local.apixml,"/*/directResponse")>
				<cfif arrayLen(local.rawResponse) is 1>
					<cfset local.rawResponse = local.rawResponse[1].xmlText>
				<cfelse>
					<cfset local.rawResponse = "">
				</cfif>
				
				<cfif len(local.rawResponse)>
					<cfscript>
					local.rawResponse2 = replace(local.rawResponse,'||','| | ','ALL');
					local.arrRawResponse = listToArray(local.rawResponse2,'|');

					local.response.responseCode = trim(local.arrRawResponse[1]);
					local.response.responseSubCode = trim(local.arrRawResponse[2]);
					local.response.responseReasonCode = trim(local.arrRawResponse[3]);
					local.response.responseReasonText = trim(local.arrRawResponse[4]);
					local.response.authorizationCode = trim(local.arrRawResponse[5]);
					local.response.avsResponse = trim(local.arrRawResponse[6]);
					local.response.transactionID = trim(local.arrRawResponse[7]);
					local.response.description = trim(local.arrRawResponse[9]);
					local.response.cardCodeResponse = trim(local.arrRawResponse[39]);		// M = Match N = No Match P = Not Processed S = Should have been present U = Issuer unable to process request
					local.response.accountNumber = trim(local.arrRawResponse[51]);		// xxxx9999
					local.response.cardType = trim(local.arrRawResponse[52]);				// Visa, MasterCard, American Express, Discover, Diners Club, JCB
					</cfscript>
				<cfelse>
					<cfset local.response.responseCode = 1>
				</cfif>			
				
			<cfelse>
				<cfset local.response.responseCode = 3>
				<cfset local.response.responseReasonText = "">
				<cfset local.arrErrorNodes = XMLSearch(local.apixml,"/*/messages/message")>
				<cfloop from="1" to="#ArrayLen(local.arrErrorNodes)#" index="local.errorNode">
					<cfset local.en_code = XMLSearch(local.arrErrorNodes[local.errorNode],"code")>
					<cfif arrayLen(local.en_code) is 1>
						<cfset local.en_code = local.en_code[1].xmlText>
					<cfelse>
						<cfset local.en_code = "">
					</cfif>
					<cfset local.en_text = XMLSearch(local.arrErrorNodes[local.errorNode],"text")>
					<cfif arrayLen(local.en_text) is 1>
						<cfset local.en_text = local.en_text[1].xmlText>
					<cfelse>
						<cfset local.en_text = "">
					</cfif>
					<cfset local.response.responseReasonText = local.response.responseReasonText & "[#local.en_code#] #local.en_text# ">
				</cfloop>
			</cfif>			
		<cfcatch type="any">
			<cfset local.response.responseCode = 3>
			<cfset local.response.responseReasonText = cfcatch.message & " " & cfcatch.detail>
		</cfcatch>
		</cftry>

		<!--- Log API call with redacted credentials --->
		<cfset local.strLog = {
			request = {
				method="POST",
				endpoint=local.AuthURL,
				bodycontent=maskXMLRequestBody(xmlRequestBody=toString(arguments.xmlRequest))
			}, response = {
				bodycontent=local.response.rawResponse,
				headers=structKeyExists(local, "APIResult") ? local.APIResult.responseheader : {},
				statuscode=structKeyExists(local, "APIResult") ? local.APIResult.status_code : ""
			}}>
		<cfset logAPICall(strCall=local.strLog)>

		<cfreturn local.response>
	</cffunction>

	<cffunction name="getTransaction" access="public" returntype="struct" output="no">
		<cfargument name="CIMUsername" type="string" required="yes">
		<cfargument name="CIMPassword" type="string" required="yes">
		<cfargument name="x_transid" type="string" required="yes">

		<cfset var local = structNew()>
		<cfset local.returnStruct = { rawResponse="", responseCode=3, responseReasonText="", responseReasonCode="", strResponseData={} }>
	
		<!--- create request --->
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<getTransactionDetailsRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#arguments.CIMUsername#</name>
						<transactionKey>#arguments.CIMPassword#</transactionKey>
					</merchantAuthentication>
					<transId>#XMLFormat(arguments.x_transid)#</transId>
				</getTransactionDetailsRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfset local.returnStruct.rawResponse = local.apiResponse.rawResponse>
			<cfset local.returnStruct.responseCode = local.apiResponse.responseCode>
			<cfset local.returnStruct.responseReasonText = local.apiResponse.responseReasonText>
			<cfset local.returnStruct.responseReasonCode = local.apiResponse.responseReasonCode>

			<cfset local.returnStruct.strResponseData = { transId:'', authCode:'', amount:'', cardType:'', cardNumber:'', tokenNumber:'', transStatus:'', description:'', transDate:''}>
			<cfif local.returnStruct.responseCode eq 1 and isXML(local.apiResponse.rawResponse)>
				<cfset local.arrTransNode = XMLSearch(local.apiResponse.rawResponse,"/getTransactionDetailsResponse/transaction")>
				<cfif arrayLen(local.arrTransNode)>
					<cfset local.returnStruct.strResponseData.transId = arrTransNode[1].transId.XmlText>
					<cfset local.returnStruct.strResponseData.authCode = arrTransNode[1].authCode.XmlText>
					<cfset local.returnStruct.strResponseData.amount = dollarFormat(arrTransNode[1].authAmount.XmlText)>
					<cfif structKeyExists(arrTransNode[1].payment,"creditcard")>
						<cfset local.returnStruct.strResponseData.cardType = arrTransNode[1].payment.creditcard.cardType.XmlText>
						<cfset local.returnStruct.strResponseData.cardNumber = arrTransNode[1].payment.creditcard.cardNumber.XmlText>
					<cfelseif structKeyExists(arrTransNode[1].payment,"tokenInformation")>
						<!--- Apple's fake card number tied to this token transaction --->
						<cfset local.returnStruct.strResponseData.tokenNumber = arrTransNode[1].payment.tokenInformation.tokenNumber.XmlText>
					</cfif>
					<cfset local.returnStruct.strResponseData.transStatus = arrTransNode[1].transactionStatus.XmlText>
					<cfset local.returnStruct.strResponseData.description = arrTransNode[1].order.description.XmlText>

					<cfset local.transactionDate = replaceNoCase(local.arrTransNode[1].submitTimeLocal.XmlText,"T"," ")>
					<cfset local.returnStruct.strResponseData.transDate = "#DateFormat(local.transactionDate,"m/d/yyyy")# #TimeFormat(local.transactionDate,"h:mm tt")#">
				</cfif>
			</cfif>
		<cfcatch type="any">
			<cfset local.returnStruct.responseReasonText = local.returnStruct.responseReasonText & cfcatch.message>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="recordMissingTransaction" access="public" returntype="boolean" output="no">
		<cfargument name="depomemberdataid" type="numeric" required="yes">
		<cfargument name="transactionNode" type="xml" required="yes">
		<cfargument name="merchantOrgCode" type="string" required="no" default="">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.AuthorizeTransactionID = arguments.transactionNode.transId.XmlText>
			<cfset local.cardType = left(arguments.transactionNode.payment.creditcard.cardType.XmlText,1)>
			<cfset local.transactionDate = replaceNoCase(arguments.transactionNode.submitTimeLocal.XmlText,"T"," ")>
			<cfset local.cardNumber = arguments.transactionNode.payment.creditcard.cardNumber.XmlText>
			<cfset local.amount = arguments.transactionNode.authAmount.XmlText>
			<cfset local.responsereasoncode = arguments.transactionNode.responseReasonCode.XmlText>
			<cfset local.approvalcode = arguments.transactionNode.authCode.XmlText>
			<cfset local.responsecode = arguments.transactionNode.responseCode.XmlText>
			<cfset local.transactionDetail = 'Credit Card Payment - #local.cardNumber#'>

			<cfquery name="local.qryInsertTrans" datasource="#application.settings.dsn.trialsmith.dsn#">
				insert into dbo.depotransactions (description, AmountBilled, SalesTaxAmount, datepurchased, depomemberdataid, 
					sourcestate, approvalCode, paymentmethod, ccTransactionID, ccResponseCode, ccResponseReasonCode, isPayment,
					merchantOrgCode, refundableAmount)
				select 
					<cfqueryparam value="#local.transactionDetail#" cfsqltype="CF_SQL_VARCHAR">, 
					<cfqueryparam value="#local.amount * -1#" cfsqltype="CF_SQL_DOUBLE">,
					0,
					<cfqueryparam value="#DateTimeFormat(local.transactionDate,'m/d/yyyy HH:nn')#" cfsqltype="CF_SQL_TIMESTAMP">, 
					<cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">,
					tlamemberstate, 
					<cfqueryparam value="#local.approvalcode#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#local.cardType#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#local.AuthorizeTransactionID#" cfsqltype="CF_SQL_VARCHAR">,
					<cfqueryparam value="#local.responsecode#" cfsqltype="CF_SQL_INTEGER">,
					<cfqueryparam value="#local.responsereasoncode#" cfsqltype="CF_SQL_INTEGER">, 
					1,
					NULLIF(<cfqueryparam value="#arguments.merchantOrgCode#" cfsqltype="CF_SQL_VARCHAR">,''),
					<cfqueryparam value="#local.amount#" cfsqltype="CF_SQL_DOUBLE">
				from dbo.depomemberdata
				where depomemberdataid = <cfqueryparam value="#arguments.depomemberdataid#" cfsqltype="CF_SQL_INTEGER">
			</cfquery>

			<cfset local.success = true>

		<cfcatch type="any">
			<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
			<cfset local.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.success>
	</cffunction>

	<cffunction name="isValidCustomerProfile" access="private" returntype="boolean" output="no">
		<cfargument name="merchantOrgcode" type="string" required="yes">
		<cfargument name="customerProfileId" type="string" required="yes">
		
		<cfset var local = structNew()>

		<cfif NOT len(arguments.customerProfileID)>
			<cfreturn false>
		</cfif>

		<cfquery name="local.qryGetCIMInfo" datasource="#application.settings.dsn.trialsmith.dsn#">
			select authUsername, authTransKey
			from depoTLA 
			where [state] = <cfqueryparam value="#arguments.merchantOrgcode#" cfsqltype="CF_SQL_VARCHAR">
			and authCIM = 1
		</cfquery>

		<cfset local.validCustomerProfileId = "">
			
		<cftry>
			<cfxml variable="local.xmlRequest">
				<cfoutput>
				<getCustomerProfileRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
					<merchantAuthentication>
						<name>#local.qryGetCIMInfo.authUsername#</name>
						<transactionKey>#local.qryGetCIMInfo.authTransKey#</transactionKey>
					</merchantAuthentication>
					<customerProfileId>#arguments.customerProfileID#</customerProfileId>
					<includeIssuerInfo>false</includeIssuerInfo>
				</getCustomerProfileRequest>
				</cfoutput>
			</cfxml>
			<cfset local.apiResponse = callGateway(local.xmlRequest)>

			<cfif local.apiResponse.responseCode is 1>
				<cfset local.validCustomerProfileId = XMLSearch(local.apiResponse.rawresponse,"string(/*/customerProfileId/text())")>
			<cfelse>
				<!--- need to catch if profile was not found --->
				<cfif findNoCase("[E00040]",local.apiResponse.responseReasonText)>
					<!--- do nothing. --->
				<cfelse>
					<cfthrow message="Error retrieving customer profile.">
				</cfif>
			</cfif>			
		<cfcatch type="any">
			<cfset local.validCustomerProfileId = "">
			<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		</cfcatch>
		</cftry>
		
		<cfreturn len(local.validCustomerProfileId) GT 0>
	</cffunction>

	<cffunction name="callAuthorize" access="private" returntype="struct" output="no">
		<cfargument name="apiRequestBody" type="string" required="yes">
		<cfargument name="arrKeysToMask" type="array" required="no" default="#arrayNew(1)#">
	
		<cfset var local = StructNew()>
		<cfset local.returnStruct = { "success":false, "arrErrors":[], "rawAPIResponse":"", "strAPIResponse":{} }>

		<cfif variables.x_testmode is 1>
			<cfset local.AuthURL = "https://apitest.authorize.net/xml/v1/request.api">
		<cfelse>
			<cfset local.AuthURL = "https://api.authorize.net/xml/v1/request.api">
		</cfif>

		<cfset local.requestTimeout = (variables.requesttimeout-2)>
	
		<cftry>
			<cfhttp url="#local.AuthURL#" method="post" throwonerror="yes" result="local.APIResult" timeout="#local.requestTimeout#" charset="utf-8">
				<cfhttpparam type="header" name="Content-Type" value="application/json">
				<cfhttpparam type="body" value="#arguments.apiRequestBody#">
			</cfhttp>

			<cfset local.apiFileContent = local.APIResult.fileContent>
			<cfset local.apiFileContentPOS = find('{',local.apiFileContent)>
			<cfif local.apiFileContentPOS gt 0>
				<cfset local.apiFileContent = RemoveChars(local.apiFileContent,1,local.apiFileContentPOS-1)>
			</cfif>
			<cfset local.returnStruct.rawAPIResponse = local.apiFileContent>
			<cfset local.returnStruct.strAPIResponse = deserializeJSON(local.returnStruct.rawAPIResponse)>

			<cfset local.strLog = { 
				request = { 
					method="POST", 
					endpoint=local.AuthURL,
					bodycontent=maskRequestBody(apiRequestBody=arguments.apiRequestBody, arrKeysToMask=arguments.arrKeysToMask)
				}, response = {
					bodycontent=local.returnStruct.strAPIResponse,
					headers=local.APIResult.responseheader,
					statuscode=local.APIResult.status_code
				}}>
			<cfset logAPICall(strCall=local.strLog)>

			<cfif local.returnStruct.strAPIResponse.messages.resultCode EQ 'Error' AND isDefined("local.returnStruct.strAPIResponse.messages.message")>
				<cfloop array="#local.returnStruct.strAPIResponse.messages.message#" index="local.thisErr">
					<cfset local.returnStruct.arrErrors.append(local.thisErr)>
				</cfloop>
			</cfif>
			
			<cfset local.returnStruct.success = true>
		<cfcatch type="any">
			<cfset local.returnStruct.arrErrors.append({ "text":cfcatch.message, "code":"callAuthorize_catch" })>
		</cfcatch>
		</cftry>
	
		<cfreturn local.returnStruct>
	</cffunction>

	<cffunction name="maskRequestBody" access="private" output="false" returntype="struct">
		<cfargument name="apiRequestBody" type="string" required="true">
		<cfargument name="arrKeysToMask" type="array" required="true">

		<cfset var local = structNew()>
		<cfset local.apiRequestBodyForLogging = deserializeJSON(arguments.apiRequestBody)>

		<cfif arrayLen(arguments.arrKeysToMask)>
			<cfloop array="#arguments.arrKeysToMask#" index="local.thisKeySet">
				<cfset local.thisKeyFullPath = "local.apiRequestBodyForLogging.#listDeleteAt(local.thisKeySet.key,listLen(local.thisKeySet.key,'.'),'.')#">
				<cfif isDefined("#local.thisKeyFullPath#")>
					<cfset structUpdate(structGet(local.thisKeyFullPath),listLast(local.thisKeySet.key,'.'),local.thisKeySet.replacement)>
				</cfif>
			</cfloop>
		</cfif>

		<cfreturn local.apiRequestBodyForLogging>
	</cffunction>

	<cffunction name="maskXMLRequestBody" access="private" output="false" returntype="string">
		<cfargument name="xmlRequestBody" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.maskedXML = arguments.xmlRequestBody>

		<!--- Redact transaction keys --->
		<cfset local.maskedXML = reReplaceNoCase(local.maskedXML,
			'<transactionKey>[^<]*</transactionKey>',
			'<transactionKey>REDACTED</transactionKey>', 'ALL')>

		<!--- Redact API login names --->
		<cfset local.maskedXML = reReplaceNoCase(local.maskedXML,
			'<name>[^<]*</name>',
			'<name>REDACTED</name>', 'ALL')>

		<cfreturn local.maskedXML>
	</cffunction>
	
	<cffunction name="logAPICall" output="false" access="private" returntype="void">
		<cfargument name="strCall" type="struct" required="true">

		<cfset var local = structnew()>

		<cftry>
			<cfset local.strRequest = {
				"c":"AuthorizeNet",
				"d": {
					"request": {
						"method":arguments.strCall.request.method,
						"endpoint":arguments.strCall.request.endpoint,
						"bodycontent":arguments.strCall.request.bodycontent
					},
					"response": {
						"bodycontent":arguments.strCall.response.bodycontent,
						"headers":arguments.strCall.response.headers,
						"statuscode":arguments.strCall.response.statuscode
					},
					"timestamp":now()
				}
			}>
	
			<cfquery name="local.qryInsertMongoQueue" datasource="#application.settings.dsn.trialsmith.dsn#">
				INSERT INTO platformQueue.dbo.queue_mongo (msgjson) 
				VALUES (<cfqueryparam cfsqltype="CF_SQL_LONGVARCHAR" value="#serializeJSON(local.strRequest)#">)
			</cfquery>
		<cfcatch type="any">
			<cfsilent><cf_tlaexception cfcatch="#cfcatch#"></cfsilent>
		</cfcatch>
		</cftry>
	</cffunction>

</cfcomponent>