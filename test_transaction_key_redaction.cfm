<cfscript>
// Test script to validate transaction key redaction implementations
// This script tests the redaction functions without making actual API calls

writeOutput("<h2>Transaction Key Redaction Validation Test</h2>");

// Test 1: Legacy XML Redaction Function
writeOutput("<h3>Test 1: Legacy XML Redaction (tsChargeCardCIM.cfc)</h3>");

try {
    // Create instance of legacy component
    local.legacyComponent = createObject("component", "models.trialsmith.tsChargeCardCIM");
    
    // Test XML with transaction key
    local.testXML = '<?xml version="1.0" encoding="utf-8"?>
    <createCustomerProfileRequest xmlns="AnetApi/xml/v1/schema/AnetApiSchema.xsd">
        <merchantAuthentication>
            <name>testuser123</name>
            <transactionKey>secretkey456</transactionKey>
        </merchantAuthentication>
        <profile>
            <merchantCustomerId>customer123</merchantCustomerId>
        </profile>
    </createCustomerProfileRequest>';
    
    // Test the maskXMLRequestBody function
    local.maskedXML = local.legacyComponent.maskXMLRequestBody(xmlRequestBody=local.testXML);
    
    writeOutput("<p><strong>Original XML:</strong></p>");
    writeOutput("<pre>" & encodeForHTML(local.testXML) & "</pre>");
    
    writeOutput("<p><strong>Redacted XML:</strong></p>");
    writeOutput("<pre>" & encodeForHTML(local.maskedXML) & "</pre>");
    
    // Verify redaction worked
    if (findNoCase("secretkey456", local.maskedXML)) {
        writeOutput("<p style='color:red;'><strong>❌ FAILED:</strong> Transaction key still visible in XML</p>");
    } else if (findNoCase("REDACTED", local.maskedXML)) {
        writeOutput("<p style='color:green;'><strong>✅ PASSED:</strong> Transaction key successfully redacted in XML</p>");
    } else {
        writeOutput("<p style='color:orange;'><strong>⚠️ WARNING:</strong> Transaction key not found but redaction unclear</p>");
    }
    
    if (findNoCase("testuser123", local.maskedXML)) {
        writeOutput("<p style='color:red;'><strong>❌ FAILED:</strong> API login name still visible in XML</p>");
    } else if (findNoCase("REDACTED", local.maskedXML)) {
        writeOutput("<p style='color:green;'><strong>✅ PASSED:</strong> API login name successfully redacted in XML</p>");
    }
    
} catch (any e) {
    writeOutput("<p style='color:red;'><strong>❌ ERROR:</strong> " & e.message & "</p>");
}

// Test 2: MemberCentral JSON Redaction Function  
writeOutput("<h3>Test 2: MemberCentral JSON Redaction (tsChargeCardCIM.cfc)</h3>");

try {
    // Create instance of MemberCentral component
    local.mcComponent = createObject("component", "membercentral.model.system.platform.gateways.tsChargeCardCIM");
    
    // Test JSON with transaction key
    local.testJSON = '{
        "createCustomerProfileRequest": { 
            "merchantAuthentication": { 
                "name": "testuser123", 
                "transactionKey": "secretkey456" 
            },
            "profile": {
                "merchantCustomerId": "customer123"
            }
        }
    }';
    
    // Test the maskRequestBody function
    local.arrKeysToMask = [
        { key='createCustomerProfileRequest.merchantAuthentication.transactionKey', replacement='REDACTED' },
        { key='createCustomerProfileRequest.merchantAuthentication.name', replacement='REDACTED' }
    ];
    
    local.maskedJSON = local.mcComponent.maskRequestBody(apiRequestBody=local.testJSON, arrKeysToMask=local.arrKeysToMask);
    local.maskedJSONString = serializeJSON(local.maskedJSON);
    
    writeOutput("<p><strong>Original JSON:</strong></p>");
    writeOutput("<pre>" & encodeForHTML(local.testJSON) & "</pre>");
    
    writeOutput("<p><strong>Redacted JSON:</strong></p>");
    writeOutput("<pre>" & encodeForHTML(local.maskedJSONString) & "</pre>");
    
    // Verify redaction worked
    if (findNoCase("secretkey456", local.maskedJSONString)) {
        writeOutput("<p style='color:red;'><strong>❌ FAILED:</strong> Transaction key still visible in JSON</p>");
    } else if (findNoCase("REDACTED", local.maskedJSONString)) {
        writeOutput("<p style='color:green;'><strong>✅ PASSED:</strong> Transaction key successfully redacted in JSON</p>");
    } else {
        writeOutput("<p style='color:orange;'><strong>⚠️ WARNING:</strong> Transaction key not found but redaction unclear</p>");
    }
    
    if (findNoCase("testuser123", local.maskedJSONString)) {
        writeOutput("<p style='color:red;'><strong>❌ FAILED:</strong> API login name still visible in JSON</p>");
    } else if (findNoCase("REDACTED", local.maskedJSONString)) {
        writeOutput("<p style='color:green;'><strong>✅ PASSED:</strong> API login name successfully redacted in JSON</p>");
    }
    
} catch (any e) {
    writeOutput("<p style='color:red;'><strong>❌ ERROR:</strong> " & e.message & "</p>");
}

// Test 3: AuthorizeCCCIM Comprehensive Redaction
writeOutput("<h3>Test 3: AuthorizeCCCIM Comprehensive Redaction</h3>");

try {
    // Test multiple API call types
    local.testAPICalls = [
        {
            name: "createTransactionRequest",
            json: '{"createTransactionRequest": {"merchantAuthentication": {"name": "testuser123", "transactionKey": "secretkey456"}}}'
        },
        {
            name: "getCustomerProfileRequest", 
            json: '{"getCustomerProfileRequest": {"merchantAuthentication": {"name": "testuser123", "transactionKey": "secretkey456"}}}'
        },
        {
            name: "createCustomerPaymentProfileRequest",
            json: '{"createCustomerPaymentProfileRequest": {"merchantAuthentication": {"name": "testuser123", "transactionKey": "secretkey456"}}}'
        }
    ];
    
    for (local.testCall in local.testAPICalls) {
        writeOutput("<p><strong>Testing " & local.testCall.name & ":</strong></p>");
        
        // Simulate the redaction logic from AuthorizeCCCIM.cfc
        local.apiRequestBodyForHistory = deserializeJSON(local.testCall.json);
        
        // Apply the comprehensive redaction logic
        local.authPaths = [
            'createTransactionRequest.merchantAuthentication',
            'createCustomerProfileRequest.merchantAuthentication', 
            'getCustomerProfileRequest.merchantAuthentication',
            'deleteCustomerPaymentProfileRequest.merchantAuthentication',
            'getTransactionDetailsRequest.merchantAuthentication',
            'createCustomerPaymentProfileRequest.merchantAuthentication',
            'updateCustomerPaymentProfileRequest.merchantAuthentication',
            'getMerchantDetailsRequest.merchantAuthentication',
            'getCustomerPaymentProfileRequest.merchantAuthentication'
        ];
        
        // Apply redaction to all authentication paths
        for (local.authPath in local.authPaths) {
            if (isDefined("local.apiRequestBodyForHistory.#local.authPath#.transactionKey"))
                structUpdate(structGet("local.apiRequestBodyForHistory.#local.authPath#"), 'transactionKey', 'REDACTED');
            if (isDefined("local.apiRequestBodyForHistory.#local.authPath#.name"))
                structUpdate(structGet("local.apiRequestBodyForHistory.#local.authPath#"), 'name', 'REDACTED');
        }
        
        local.redactedJSON = serializeJSON(local.apiRequestBodyForHistory);
        
        // Verify redaction
        if (findNoCase("secretkey456", local.redactedJSON) || findNoCase("testuser123", local.redactedJSON)) {
            writeOutput("<span style='color:red;'>❌ FAILED: Credentials still visible</span><br>");
        } else if (findNoCase("REDACTED", local.redactedJSON)) {
            writeOutput("<span style='color:green;'>✅ PASSED: Credentials successfully redacted</span><br>");
        } else {
            writeOutput("<span style='color:orange;'>⚠️ WARNING: Redaction unclear</span><br>");
        }
    }
    
} catch (any e) {
    writeOutput("<p style='color:red;'><strong>❌ ERROR:</strong> " & e.message & "</p>");
}

writeOutput("<h3>Summary</h3>");
writeOutput("<p>All redaction implementations have been tested. Review the results above to ensure all transaction keys and API login names are properly redacted.</p>");
writeOutput("<p><strong>Next Steps:</strong></p>");
writeOutput("<ul>");
writeOutput("<li>Deploy changes to test environment</li>");
writeOutput("<li>Monitor API logs to verify no credentials are exposed</li>");
writeOutput("<li>Test actual payment processing to ensure functionality is preserved</li>");
writeOutput("<li>Deploy to production during low-traffic period</li>");
writeOutput("</ul>");
</cfscript>
